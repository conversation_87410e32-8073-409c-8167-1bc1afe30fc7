import { Form, Input, Select, Card, Row, Col } from "antd";
import { useContext } from "react";
import { SettingsContext } from "../../context/SettingsContext";
import { useInputStyle } from "../../hooks/useInputStyle";

const TypographySettings = ({ form }) => {
  const { inputStyle } = useInputStyle();
  const { optionsFonts = [] } = useContext(SettingsContext);

  return (
    <div>
      <Card title="Main Text" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item 
              name={["MainTextFont"]} 
              label="Main Text Font"
            >
              <Select
                placeholder="Select font"
                style={inputStyle}
                options={optionsFonts}
                showSearch
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name={["MainTextFontSize"]} 
              label="Main Text Font Size"
            >
              <Input
                placeholder="12px"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name={["MainTextColor"]} 
              label="Main Text Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#333333"
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="Main Heading" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item 
              name={["MainHeadingFont"]} 
              label="Main Heading Font"
            >
              <Select
                placeholder="Select font"
                style={inputStyle}
                options={optionsFonts}
                showSearch
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name={["MainHeadingFontSize"]} 
              label="Main Heading Font Size"
            >
              <Input
                placeholder="22px"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name={["MainHeadingColor"]} 
              label="Main Heading Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#D14836"
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="Second Heading" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item 
              name={["SecondHeadingFont"]} 
              label="Second Heading Font"
            >
              <Select
                placeholder="Select font"
                style={inputStyle}
                options={optionsFonts}
                showSearch
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name={["SecondHeadingFontSize"]} 
              label="Second Heading Font Size"
            >
              <Input
                placeholder="18px"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name={["SecondHeadingColor"]} 
              label="Second Heading Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#000000"
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="Table Header" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item 
              name={["TableHeadFont"]} 
              label="Table Head Font"
            >
              <Select
                placeholder="Select font"
                style={inputStyle}
                options={optionsFonts}
                showSearch
                filterOption={(input, option) =>
                  option.label.toLowerCase().includes(input.toLowerCase())
                }
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item 
              name={["TableHeadFontSize"]} 
              label="Table Head Font Size"
            >
              <Input
                placeholder="14px"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item 
              name={["TableHeadColor"]} 
              label="Table Head Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#000000"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item 
              name={["TableHeadBackgroundColor"]} 
              label="Table Head Background"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#F5F5F5"
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="Links" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item 
              name={["LinkColor"]} 
              label="Link Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#D14836"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name={["LinkHoverColor"]} 
              label="Link Hover Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#D52B1E"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item 
              name={["LinkUnderline"]} 
              label="Link Underline"
            >
              <Input
                placeholder="none, underline"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="Table Styling" size="small">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item 
              name={["TableInlineBorderColor"]} 
              label="Table Inline Border Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#CCCCCC"
              />
            </Form.Item>

            <Form.Item 
              name={["TableOutlineBorderColor"]} 
              label="Table Outline Border Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#000000"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item 
              name={["TableOddRowColor"]} 
              label="Table Odd Row Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#FFFFFF"
              />
            </Form.Item>

            <Form.Item 
              name={["TableEvenRowColor"]} 
              label="Table Even Row Color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#F9F9F9"
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default TypographySettings;
