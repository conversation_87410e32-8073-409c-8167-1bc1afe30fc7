import { <PERSON>, <PERSON><PERSON>, Card, message } from "antd";
import { useState } from "react";
import { SettingsContext } from "../../context/SettingsContext";
import GeneralSettings from "../GeneralSettings";
import InstrumentGroups from "../InstrumentGroups";
import FormatSettings from "../FormatSettings";
import DisplaySettings from "../DisplaySettings";
import TypographySettings from "../TypographySettings";
import { ChartSetting } from "../ChartSetting";

// Mock data for testing
const mockBasicSettings = {
  availableLanguages: ["en-GB", "da-DK", "sv-SE"],
  instrumentIds: ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"],
  timezone: "W. Europe Standard Time",
  dataRefreshRate: 60,
};

const mockAvailableTools = {
  companyCode: "TEST",
  version: "1.0.0",
};

const mockCurrencies = ["USD", "EUR", "DKK", "SEK"];

const mockOptionsFonts = [
  { value: "arial", label: "Arial" },
  { value: "helvetica", label: "Helvetica" },
  { value: "times", label: "Times New Roman" },
  { value: "georgia", label: "Georgia" },
];

const TestDemo = () => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState("general");

  const handleSubmit = () => {
    const values = form.getFieldsValue();
    console.log("Form Values:", values);
    message.success("Form data logged to console!");
  };

  const handleReset = () => {
    form.resetFields();
    message.info("Form reset!");
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case "general":
        return <GeneralSettings form={form} />;
      case "chart":
        return <ChartSetting form={form} />;
      case "instrumentGroups":
        return <InstrumentGroups form={form} onChange={() => console.log("Groups changed")} />;
      case "format":
        return <FormatSettings form={form} />;
      case "display":
        return <DisplaySettings form={form} />;
      case "typography":
        return <TypographySettings form={form} />;
      default:
        return <GeneralSettings form={form} />;
    }
  };

  const tabs = [
    { key: "general", label: "General Settings" },
    { key: "chart", label: "Chart Settings" },
    { key: "instrumentGroups", label: "Instrument Groups" },
    { key: "format", label: "Format Settings" },
    { key: "display", label: "Display Settings" },
    { key: "typography", label: "Typography Settings" },
  ];

  return (
    <SettingsContext.Provider
      value={{
        basicSettings: mockBasicSettings,
        availableTools: mockAvailableTools,
        currencies: mockCurrencies,
        optionsFonts: mockOptionsFonts,
        initState: {},
        toolName: "fragulizer",
      }}
    >
      <div style={{ padding: "20px" }}>
        <Card title="Fragulizer Settings Demo" style={{ marginBottom: 20 }}>
          <div style={{ marginBottom: 16 }}>
            {tabs.map((tab) => (
              <Button
                key={tab.key}
                type={activeTab === tab.key ? "primary" : "default"}
                onClick={() => setActiveTab(tab.key)}
                style={{ marginRight: 8, marginBottom: 8 }}
              >
                {tab.label}
              </Button>
            ))}
          </div>

          <div style={{ marginBottom: 16 }}>
            <Button type="primary" onClick={handleSubmit} style={{ marginRight: 8 }}>
              Log Form Data
            </Button>
            <Button onClick={handleReset}>Reset Form</Button>
          </div>
        </Card>

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            EnableHeading: true,
            EnableInstrumentsTable: true,
            ShowCurrencyColumn: true,
            EnableActivityTrend: true,
            DefaultPeriod: "3M",
            EnableExcelDownload: true,
            EnablePrint: true,
            EnableJPGDownload: true,
            EnablePDFDownload: true,
            UseLatinNumbers: true,
            GoogleAnalyticsEnabled: true,
            instrumentIds: ["AAPL", "GOOGL"],
            enabledPeriods: ["1D", "5D", "1M", "3M", "6M", "1Y"],
            defaultPeriod: "1M",
            InstrumentGroups: [
              { Name: "Group 1", InstrumentIDs: ["AAPL", "MSFT"] },
              { Name: "Group 2", InstrumentIDs: ["GOOGL", "TSLA"] }
            ],
            InstrumentGroupKeys: ["group1", "group2"],
            InstrumentGroupNames: {
              group1: "Group 1",
              group2: "Group 2"
            },
            EditCustomPhrases: {
              group1: {
                "en-GB": "Group 1",
                "da-DK": "Gruppe 1",
                "sv-SE": "Grupp 1"
              },
              group2: {
                "en-GB": "Group 2",
                "da-DK": "Gruppe 2",
                "sv-SE": "Grupp 2"
              }
            },
            SharePriceNumberDecimalDigits: 2,
            SharePricePercentageDecimalDigits: 2,
            SharePriceValueIncreaseColor: "#00FF00",
            SharePriceValueDecreaseColor: "#FF0000",
            NumberOfYearOnColumnChart: 3,
            PieBorderWidth: 1,
            ChartColors: "#2F7ED8;#0D233A;#8BBC21;#910000;#1AADCE",
            ChartBGColor: "#FFFFFF",
            MainTextFont: "arial",
            MainTextFontSize: "12px",
            MainTextColor: "#333333",
            MainHeadingFont: "arial",
            MainHeadingFontSize: "22px",
            MainHeadingColor: "#D14836",
            LinkColor: "#D14836",
            LinkHoverColor: "#D52B1E",
            TimeZone: "W. Europe Standard Time",
            DefaultCulture: "en-GB",
            ShowDataProviderInfo: false,
            ShowDataDelayInfo: true,
            ShowSupplierInfo: true,
            ShowSupplierInfoLink: true,
            ShowDisclaimerInfo: true,
            ShowCookiePolicyInfo: true,
          }}
        >
          <Card title={tabs.find(t => t.key === activeTab)?.label}>
            {renderTabContent()}
          </Card>
        </Form>
      </div>
    </SettingsContext.Provider>
  );
};

export default TestDemo;
