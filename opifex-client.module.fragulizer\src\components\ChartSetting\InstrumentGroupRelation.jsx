import { Card, Tag, Alert, Divider } from "antd";
import { useContext, useMemo } from "react";
import { SettingsContext } from "../../context/SettingsContext";

const InstrumentGroupRelation = ({ form }) => {
  const { basicSettings } = useContext(SettingsContext);

  const selectedInstruments = form?.getFieldValue(["instrumentIds"]) || [];
  const instrumentGroups = form?.getFieldValue(["InstrumentGroups"]) || [];

  const relationData = useMemo(() => {
    const assigned = new Map();
    const unassigned = [];
    const groupInstruments = new Map();

    instrumentGroups.forEach((group, index) => {
      const groupName = group.Name || `Group ${index + 1}`;
      const instruments = group.InstrumentIDs || [];
      groupInstruments.set(groupName, instruments);
      
      instruments.forEach(instrumentId => {
        assigned.set(instrumentId, groupName);
      });
    });

    selectedInstruments.forEach(instrumentId => {
      if (!assigned.has(instrumentId)) {
        unassigned.push(instrumentId);
      }
    });

    return {
      assigned,
      unassigned,
      groupInstruments,
      totalSelected: selectedInstruments.length,
      totalAssigned: assigned.size
    };
  }, [selectedInstruments, instrumentGroups]);

  if (selectedInstruments.length === 0) {
    return (
      <Alert
        message="No instruments selected"
        description="Please select instruments first to see group relationships."
        type="info"
        showIcon
      />
    );
  }

  return (
    <Card title="Instrument-Group Relationships" size="small">
      <div style={{ marginBottom: 16 }}>
        <strong>Summary:</strong> {relationData.totalAssigned} of {relationData.totalSelected} selected instruments are assigned to groups
      </div>

      {/* Show instruments by group */}
      {Array.from(relationData.groupInstruments.entries()).map(([groupName, instruments]) => {
        const selectedInGroup = instruments.filter(id => selectedInstruments.includes(id));
        if (selectedInGroup.length === 0) return null;

        return (
          <div key={groupName} style={{ marginBottom: 12 }}>
            <strong>{groupName}:</strong>
            <div style={{ marginTop: 4 }}>
              {selectedInGroup.map(instrumentId => (
                <Tag key={instrumentId} color="blue" style={{ marginBottom: 4 }}>
                  {instrumentId}
                </Tag>
              ))}
            </div>
          </div>
        );
      })}

      {/* Show unassigned instruments */}
      {relationData.unassigned.length > 0 && (
        <>
          <Divider />
          <div>
            <strong>Unassigned Instruments:</strong>
            <div style={{ marginTop: 4 }}>
              {relationData.unassigned.map(instrumentId => (
                <Tag key={instrumentId} color="orange" style={{ marginBottom: 4 }}>
                  {instrumentId}
                </Tag>
              ))}
            </div>
            <Alert
              message="Some instruments are not assigned to any group"
              description="Consider assigning these instruments to appropriate groups for better organization."
              type="warning"
              showIcon
              style={{ marginTop: 8 }}
            />
          </div>
        </>
      )}

      {relationData.totalAssigned === relationData.totalSelected && relationData.totalSelected > 0 && (
        <Alert
          message="All selected instruments are properly assigned to groups"
          type="success"
          showIcon
          style={{ marginTop: 8 }}
        />
      )}
    </Card>
  );
};

export default InstrumentGroupRelation;
