import { Form, Switch, Card, Row, Col, Input } from "antd";
import { useInputStyle } from "../../hooks/useInputStyle";

const DisplaySettings = ({ form }) => {
  const { inputStyle } = useInputStyle();

  return (
    <div>
      <Card title="Price Colors" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item 
              name={["SharePriceValueIncreaseColor"]} 
              label="Share price value increase color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#00FF00"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item 
              name={["SharePriceValueDecreaseColor"]} 
              label="Share price value decrease color"
            >
              <Input
                type="color"
                style={{ ...inputStyle, height: 40 }}
                placeholder="#FF0000"
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="Footer Settings" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item 
              name={["ShowDataProviderInfo"]} 
              label="Show Data Provider Info"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item 
              name={["ShowDataDelayInfo"]} 
              label="Show Data Delay Info"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item 
              name={["ShowSupplierInfo"]} 
              label="Show Supplier Info"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item 
              name={["ShowSupplierInfoLink"]} 
              label="Show Supplier Info Link"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item 
              name={["ShowDisclaimerInfo"]} 
              label="Show Disclaimer Info"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item 
              name={["ShowCookiePolicyInfo"]} 
              label="Show Cookie Policy Info"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      <Card title="Custom Market Name" size="small">
        <Form.Item 
          name={["CustomMarketName"]} 
          label="Custom Market Name"
        >
          <Input
            placeholder="Enter custom market name"
            style={inputStyle}
          />
        </Form.Item>
      </Card>
    </div>
  );
};

export default DisplaySettings;
