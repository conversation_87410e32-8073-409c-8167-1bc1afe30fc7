import { useInputStyle } from "../../hooks/useInputStyle";
import { ReactAntdDraggableSelect } from "@euroland/react-antd-draggable-select";

const PERIOD_OPTIONS = [
  { value: "1D", label: "1D" },
  { value: "5D", label: "5D" },
  { value: "1M", label: "1M" },
  { value: "3M", label: "3M" },
  { value: "6M", label: "6M" },
  { value: "1Y", label: "1Y" },
  { value: "5Y", label: "5Y" },
  { value: "10Y", label: "10Y" },
  { value: "Custom range", label: "Custom range" },
  { value: "All", label: "All" }
];

const EnabledPeriodSelect = ({ value, onChange, form }) => {
  const { inputStyle } = useInputStyle();

  const handleChange = (_value) => {
    onChange?.(_value);
  };

  return (
    <ReactAntdDraggableSelect
      sortingStrategy="vertical"
      showSearch
      value={value}
      placeholder="Select enabled periods"
      optionFilterProp="children"
      onChange={handleChange}
      style={inputStyle}
      options={PERIOD_OPTIONS}
      allowClear
    />
  );
};

export default EnabledPeriodSelect;
