import { useContext, useRef, useMemo } from "react";

import { useInputStyle } from "../../hooks/useInputStyle";
import { ReactAntdDraggableSelect } from "@euroland/react-antd-draggable-select";
import { Select } from "antd";
import { SettingsContext } from "../../context/SettingsContext";

const InstrumentIdSelect = ({ value, onChange, form, singleMode = false }) => {
  const oldValue = useRef([]);
  const { inputStyle } = useInputStyle();
  const { basicSettings } = useContext(SettingsContext);
  const { instrumentIds } = basicSettings;

  // Get instruments that are already assigned to groups (only when groups are enabled)
  const assignedInstruments = useMemo(() => {
    if (singleMode) return new Set(); // No group assignments in single mode

    const instrumentGroups = form?.getFieldValue(["InstrumentGroups"]) || [];
    const assigned = new Set();
    instrumentGroups.forEach(group => {
      if (group.InstrumentIDs && Array.isArray(group.InstrumentIDs)) {
        group.InstrumentIDs.forEach(id => assigned.add(id));
      }
    });
    return assigned;
  }, [form, singleMode]);

  // Filter available instruments (show all, but mark assigned ones)
  const instrumentOptions = useMemo(() => {
    return instrumentIds.map((option) => ({
      value: option,
      label: !singleMode && assignedInstruments.has(option)
        ? `${option} (assigned to group)`
        : option,
      disabled: false, // Don't disable, just show info
    }));
  }, [instrumentIds, assignedInstruments, singleMode]);

  const handleChange = (_value) => {
    // In single mode, ensure only one instrument is selected
    let finalValue = _value;
    if (singleMode) {
      if (Array.isArray(_value) && _value.length > 1) {
        finalValue = [_value[_value.length - 1]]; // Keep only the last selected
      } else if (Array.isArray(_value)) {
        finalValue = _value;
      } else {
        finalValue = _value ? [_value] : [];
      }
    }

    const changedValue = Array.isArray(finalValue)
      ? finalValue.filter((item) => !oldValue.current.includes(item))
      : [];

    changedValue.forEach((instrumentId) => {
      // Initialize instrument configuration if needed
      form.setFieldValue(["instrumentConfigs", instrumentId, "enabled"], true);
      instrumentIds.forEach((id) => {
        form.setFieldValue(
          ["instrumentConfigs", instrumentId, "settings", id],
          instrumentId
        );
      });
    });

    onChange?.(finalValue);
    oldValue.current = Array.isArray(finalValue) ? [...finalValue] : [];
  };

  // Render different components based on mode
  if (singleMode) {
    return (
      <Select
        showSearch
        value={Array.isArray(value) ? value[0] : value}
        placeholder="Select single instrument for Market Share, Fragmentation & Table"
        optionFilterProp="children"
        onChange={(val) => handleChange(val ? [val] : [])}
        style={inputStyle}
        options={instrumentOptions}
        allowClear
      />
    );
  }

  return (
    <ReactAntdDraggableSelect
      sortingStrategy="vertical"
      showSearch
      value={value}
      placeholder="Select instruments"
      optionFilterProp="children"
      onChange={handleChange}
      style={inputStyle}
      options={instrumentOptions}
      allowClear
    />
  );
};

export default InstrumentIdSelect;
