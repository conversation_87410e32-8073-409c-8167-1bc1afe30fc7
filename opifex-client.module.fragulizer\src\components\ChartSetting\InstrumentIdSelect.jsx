import { useContext, useRef, useMemo } from "react";

import { useInputStyle } from "../../hooks/useInputStyle";
import { ReactAntdDraggableSelect } from "@euroland/react-antd-draggable-select";
import { SettingsContext } from "../../context/SettingsContext";

const InstrumentIdSelect = ({ value, onChange, form }) => {
  const oldValue = useRef([]);
  const { inputStyle } = useInputStyle();
  const { basicSettings } = useContext(SettingsContext);
  const { instrumentIds } = basicSettings;

  // Get instruments that are already assigned to groups
  const assignedInstruments = useMemo(() => {
    const instrumentGroups = form?.getFieldValue(["InstrumentGroups"]) || [];
    const assigned = new Set();
    instrumentGroups.forEach(group => {
      if (group.InstrumentIDs && Array.isArray(group.InstrumentIDs)) {
        group.InstrumentIDs.forEach(id => assigned.add(id));
      }
    });
    return assigned;
  }, [form]);

  // Filter available instruments (show all, but mark assigned ones)
  const instrumentOptions = useMemo(() => {
    return instrumentIds.map((option) => ({
      value: option,
      label: assignedInstruments.has(option)
        ? `${option} (assigned to group)`
        : option,
      disabled: false, // Don't disable, just show info
    }));
  }, [instrumentIds, assignedInstruments]);

  const handleChange = (_value) => {
    const changedValue = _value.filter(
      (item) => !oldValue.current.includes(item)
    );
    changedValue.forEach((instrumentId) => {
      // Initialize instrument configuration if needed
      form.setFieldValue(["instrumentConfigs", instrumentId, "enabled"], true);
      instrumentIds.forEach((id) => {
        form.setFieldValue(
          ["instrumentConfigs", instrumentId, "settings", id],
          instrumentId
        );
      });
    });
    onChange?.(_value);
    oldValue.current = [..._value];
  };

  return (
    <ReactAntdDraggableSelect
      sortingStrategy="vertical"
      showSearch
      value={value}
      placeholder="Select instruments"
      optionFilterProp="children"
      onChange={handleChange}
      style={inputStyle}
      options={instrumentOptions}
      allowClear
    />
  );
};

export default InstrumentIdSelect;
