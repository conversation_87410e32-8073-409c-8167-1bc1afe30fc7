import { useContext, useRef } from "react";

import { useInputStyle } from "../../hooks/useInputStyle";
import { ReactAntdDraggableSelect } from "@euroland/react-antd-draggable-select";
import { SettingsContext } from "../../context/SettingsContext";

const InstrumentIdSelect = ({ value, onChange, form }) => {
  const oldValue = useRef([]);
  const { inputStyle } = useInputStyle();
  const { basicSettings } = useContext(SettingsContext);
  const { instrumentIds } = basicSettings;
  
  const handleChange = (_value) => {
    const changedValue = _value.filter(
      (item) => !oldValue.current.includes(item)
    );
    changedValue.forEach((currencyCode) => {
      form.setFieldValue(["currencyEdit", currencyCode, "DecimalDigits"], 2);
      instrumentIds.forEach((id) => {
        form.setFieldValue(
          ["currencyEdit", currencyCode, "Text", id],
          currencyCode
        );
      });
    });
    onChange?.(_value);
    oldValue.current = [..._value];
  };

  return (
    <ReactAntdDraggableSelect
      sortingStrategy="vertical"
      showSearch
      value={value}
      placeholder="Select a currency"
      optionFilterProp="children"
      onChange={handleChange}
      style={inputStyle}
      options={instrumentIds.map((option) => ({
        value: option,
        label: option,
      }))}
      allowClear
    />
  );
};

export default InstrumentIdSelect;
