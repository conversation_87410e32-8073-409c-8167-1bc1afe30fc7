{"ConnectionStrings": {"OpifexDb": "Server=**********;Database=Opifex2;User ID=uShark;Password=**********;TrustServerCertificate=True", "SharkDb": "Server=**********;Database=shark;User ID=uShark;Password=**********;TrustServerCertificate=True", "ShareGraph": "Server=**********;Database=ShareGraph;User ID=uShark;Password=**********;TrustServerCertificate=True", "SSDb": "Server=**********;Database=SS;User ID=uShark;Password=**********;TrustServerCertificate=True"}, "Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}, "SDataApiUrl": "https://localhost:5005/graphql", "AppSettings": {"AllowedHosts": ["http://localhost:3000", "http://localhost:3001", "http://localhost:5000", "http://localhost:8222", "https://localhost:5002", "https://localhost:6002", "https://localhost:7002", "https://localhost:8002", "https://localhost:9002", "https://127.0.0.1:5002"], "Tools": [{"Value": "<PERSON><PERSON><PERSON><PERSON>", "Name": "<PERSON><PERSON><PERSON><PERSON>", "ShortName": "<PERSON><PERSON><PERSON><PERSON>", "DevUrl": "https://dev.vn.euroland.com/tools/fragulizer", "OfficialUrl": "https://dev.vn.euroland.com/tools/fragulizer", "UploadedPath": "/sofs/Shared_storage/tools-settings", "ToolSettingFolderPath": "..\\..\\tools\\fragulizer-api\\Config\\Company", "GammaPath": "\\migration\\gamma\\Company", "WebcatPath": "\\migration\\webcat\\Company", "CustomPharsesPath": "\\fragulizer\\src\\Fragulizer.Web\\Translations", "SSServiceId": 88, "EntryModuleUrl": "http://localhost:4009/mf-manifest.json", "ToolNameIntegration": "fragulizer", "ValidationRulesPath": "/ValidationRules/fragulizer.xml"}]}, "UseDevSpace": false}