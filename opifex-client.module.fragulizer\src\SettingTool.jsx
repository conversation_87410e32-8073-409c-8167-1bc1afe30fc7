import { Alert, Form, Tabs } from "antd";
import { useEffect, useImperativeHandle, useMemo } from "react";
import CustomCSSEditor from "./components/CustomCSSEditor";
import StyleURI from "./components/StyleUri";
import { ChartSetting } from "./components/ChartSetting";
import { CustomPhrases } from "./components/CustomPhrases";
import { SettingsContext } from "./context/SettingsContext";
import { DEFAULT_DECIMAL_DIGITS_CURRENCY } from "./constant/commom";
import { isObjectAndNotArray } from "./utils/common";

export default function SettingTool({
  handleRef,
  initState,
  submitErrors,
  onDraftChange,
  basicSettings,
  availableTools,
  currencies,
  defaultFonts,
  companyFonts,
  toolName,
}) {
  const [form] = Form.useForm();

  const {
    currencyEdit = {},
    currencyCodes = [],
    Currencies,
    InstrumentConfigs,
    Instruments,
  } = initState;
  const { availableLanguages = [] } = basicSettings;
  console.log({ initState });
  const initCurrencyState = useMemo(() => {
    const needInitCurrencyCodeAndCurrencyEdit =
      !currencyCodes.length && !Object.keys(currencyEdit).length;
    const newCurrencyEdit = {};
    const newCurrencyCodes = [];
    if (
      needInitCurrencyCodeAndCurrencyEdit &&
      isObjectAndNotArray(Currencies) &&
      Currencies?.Currency
    ) {
      Currencies.Currency.forEach((item) => {
        const code = item.Code;
        newCurrencyCodes.push(code);
        const Text = { ...(item.Text || {}) };
        availableLanguages.forEach((lang) => {
          if (!Text[lang]) Text[lang] = code;
        });
        newCurrencyEdit[code] = {
          ...item,
          Text,
          DecimalDigits: item.DecimalDigits || DEFAULT_DECIMAL_DIGITS_CURRENCY,
        };
      });
      return {
        currencyCodes: newCurrencyCodes,
        currencyEdit: newCurrencyEdit,
      };
    }

    return { currencyCodes, currencyEdit };
  }, [currencyEdit, currencyCodes, Currencies, availableLanguages]);

  const handleValidate = async () => {
    try {
      const values = await form.validateFields();

      console.log("Success: remote lst", values);
      return true;
    } catch (errorInfo) {
      console.log("Failed: remote lst", errorInfo);
    }
  };

  const handleSubmit = async () => {
    const data = form.getFieldsValue();
    console.log("handleSubmit", data);
  };

  useImperativeHandle(handleRef, () => {
    return { onSubmit: handleSubmit, onValidate: handleValidate };
  });

  const initInstrumentConfigState = useMemo(() => {
    const needUpdateInstrumentConfigs = !InstrumentConfigs;
    const insConfigsDraft = {};

    if (needUpdateInstrumentConfigs && Instruments?.Instrument) {
      Instruments.Instrument.forEach((ins) => {
        const insId = ins.Id;
        insConfigsDraft[insId] = {
          EnabledDividendOption: ins.EnabledDividendOption ?? true,
          LimitInvestmentStartDate: ins.LimitInvestmentStartDate,
          LimitStartingData: ins.LimitStartingData,
        };
      });
      return {
        InstrumentConfigs: insConfigsDraft,
      };
    }

    return {};
  }, [InstrumentConfigs, Instruments]);

  const handleFormDataChange = (_, values) => {
    onDraftChange(form.getFieldsValue(true));
  };
  const IC_SETTING_TAB = [
    {
      index: 1,
      key: "Chart",
      label: "Chart",
      children: <ChartSetting form={form} />,
    },
    {
      index: 3,
      key: "customPhrase",
      label: "Custom Phrase",
      children: <CustomPhrases form={form} onChange={handleFormDataChange} />,
    },
    {
      index: 4,
      key: "styleUri",
      label: "Style Uri",
      children: <StyleURI />,
    },
    {
      index: 5,
      key: "customCss",
      label: "Custom CSS",
      children: <CustomCSSEditor form={form} onChange={handleFormDataChange} />,
    },
  ];

  useEffect(() => {
    const data = form.getFieldsValue(true);
    onDraftChange(data);
  }, []);

  const optionsFonts = Array?.from(
    new Map(companyFonts?.map((font) => [font.fontFamily, font])).values()
  )
    .map((font) => ({
      value: font.id,
      label: font.fontFamily,
    }))
    .concat(defaultFonts?.map((font) => ({ value: font, label: font })));
  return (
    <div>
      {submitErrors
        ? Object.values(submitErrors)
            .flat()
            .map((item, index) => (
              <Alert
                closable
                className="mt-3"
                key={index}
                message={item}
                type="error"
              />
            ))
        : null}
      <SettingsContext.Provider
        value={{
          basicSettings,
          availableTools,
          initState,
          currencies,
          optionsFonts,
          toolName,
        }}
      >
        <Form
          layout={"vertical"}
          form={form}
          initialValues={{
            ...initState,
            ...initCurrencyState,
            ...initInstrumentConfigState,
          }}
          onValuesChange={handleFormDataChange}
          style={{ width: "100%" }}
        >
          <Tabs
            tabPosition={"left"}
            size={"default"}
            defaultActiveKey="1"
            items={IC_SETTING_TAB}
          />
        </Form>
      </SettingsContext.Provider>
    </div>
  );
}
