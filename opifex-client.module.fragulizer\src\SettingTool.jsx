import { Alert, Form, Tabs } from "antd";
import { useEffect, useImperativeHandle, useMemo, useState, useCallback } from "react";
import CustomCSSEditor from "./components/CustomCSSEditor";
import StyleURI from "./components/StyleUri";
import { ChartSetting } from "./components/ChartSetting";
import { CustomPhrases } from "./components/CustomPhrases";
import GeneralSettings from "./components/GeneralSettings";
import InstrumentGroups from "./components/InstrumentGroups";
import FormatSettings from "./components/FormatSettings";
import DisplaySettings from "./components/DisplaySettings";
import { SettingsContext } from "./context/SettingsContext";
import { DEFAULT_DECIMAL_DIGITS_CURRENCY } from "./constant/commom";
import { isObjectAndNotArray } from "./utils/common";
import Download from "./components/Download";

export default function SettingTool({
  handleRef,
  initState,
  submitErrors,
  onDraftChange,
  basicSettings,
  availableTools,
  currencies,
  defaultFonts,
  companyFonts,
  toolName,
}) {
  const [form] = Form.useForm();

  const {
    currencyEdit = {},
    currencyCodes = [],
    Currencies,
    InstrumentConfigs,
    Instruments,
  } = initState;
  const { availableLanguages = [] } = basicSettings;
  const initCurrencyState = useMemo(() => {
    const needInitCurrencyCodeAndCurrencyEdit =
      !currencyCodes.length && !Object.keys(currencyEdit).length;
    const newCurrencyEdit = {};
    const newCurrencyCodes = [];
    if (
      needInitCurrencyCodeAndCurrencyEdit &&
      isObjectAndNotArray(Currencies) &&
      Currencies?.Currency
    ) {
      Currencies.Currency.forEach((item) => {
        const code = item.Code;
        newCurrencyCodes.push(code);
        const Text = { ...(item.Text || {}) };
        availableLanguages.forEach((lang) => {
          if (!Text[lang]) Text[lang] = code;
        });
        newCurrencyEdit[code] = {
          ...item,
          Text,
          DecimalDigits: item.DecimalDigits || DEFAULT_DECIMAL_DIGITS_CURRENCY,
        };
      });
      return {
        currencyCodes: newCurrencyCodes,
        currencyEdit: newCurrencyEdit,
      };
    }

    return { currencyCodes, currencyEdit };
  }, [currencyEdit, currencyCodes, Currencies, availableLanguages]);

  const handleValidate = async () => {
    try {
      const values = await form.validateFields();

      console.log("Success: remote lst", values);
      return true;
    } catch (errorInfo) {
      console.log("Failed: remote lst", errorInfo);
    }
  };

  const handleSubmit = async () => {
    const data = form.getFieldsValue();
    console.log("handleSubmit", data);
  };

  useImperativeHandle(handleRef, () => {
    return { onSubmit: handleSubmit, onValidate: handleValidate };
  });

  const initInstrumentConfigState = useMemo(() => {
    const needUpdateInstrumentConfigs = !InstrumentConfigs;
    const insConfigsDraft = {};

    if (needUpdateInstrumentConfigs && Instruments?.Instrument) {
      Instruments.Instrument.forEach((ins) => {
        const insId = ins.Id;
        insConfigsDraft[insId] = {
          EnabledDividendOption: ins.EnabledDividendOption ?? true,
          LimitInvestmentStartDate: ins.LimitInvestmentStartDate,
          LimitStartingData: ins.LimitStartingData,
        };
      });
      return {
        InstrumentConfigs: insConfigsDraft,
      };
    }

    return {};
  }, [InstrumentConfigs, Instruments]);

  const initPeriodState = useMemo(() => {
    const { enabledPeriods, defaultPeriod } = initState;

    const defaultEnabledPeriods = enabledPeriods || ["1D", "5D", "1M", "3M", "6M", "1Y"];
    const defaultDefaultPeriod = defaultPeriod || "1M";

    return {
      enabledPeriods: defaultEnabledPeriods,
      defaultPeriod: defaultDefaultPeriod,
    };
  }, [initState]);

  const initGeneralState = useMemo(() => {
    const { availableLanguages = [] } = basicSettings;

    // Initialize default instrument groups
    const defaultGroups = initState.InstrumentGroups || [
      { Name: "Group 1", InstrumentIDs: [] },
      { Name: "Group 2", InstrumentIDs: [] }
    ];

    // Initialize multi-language phrases for groups
    const defaultCustomPhrases = initState.EditCustomPhrases || {};
    defaultGroups.forEach((group, index) => {
      const groupKey = `group${index + 1}`;
      if (!defaultCustomPhrases[groupKey]) {
        defaultCustomPhrases[groupKey] = {};
        availableLanguages.forEach(lang => {
          defaultCustomPhrases[groupKey][lang] = group.Name;
        });
      }
    });

    return {
      EnableHeading: initState.EnableHeading ?? true,
      EnableInstrumentsTable: initState.EnableInstrumentsTable ?? true,
      ShowCurrencyColumn: initState.ShowCurrencyColumn ?? true,
      EnableActivityTrend: initState.EnableActivityTrend ?? true,
      DefaultPeriod: initState.DefaultPeriod || "3M",
      EnableExcelDownload: initState.EnableExcelDownload ?? true,
      EnablePrint: initState.EnablePrint ?? true,
      EnableJPGDownload: initState.EnableJPGDownload ?? true,
      EnablePDFDownload: initState.EnablePDFDownload ?? true,
      UseLatinNumbers: initState.UseLatinNumbers ?? true,
      GoogleAnalyticsEnabled: initState.GoogleAnalyticsEnabled ?? true,
      InstrumentGroups: defaultGroups,
      InstrumentGroupKeys: ["group1", "group2"],
      InstrumentGroupNames: {
        group1: "Group 1",
        group2: "Group 2"
      },
      EditCustomPhrases: defaultCustomPhrases,
      SharePriceNumberDecimalDigits: initState.SharePriceNumberDecimalDigits || 2,
      SharePricePercentageDecimalDigits: initState.SharePricePercentageDecimalDigits || 2,
      SharePriceValueIncreaseColor: initState.SharePriceValueIncreaseColor || "Green",
      SharePriceValueDecreaseColor: initState.SharePriceValueDecreaseColor || "Red",
      NumberOfYearOnColumnChart: initState.NumberOfYearOnColumnChart || 3,
      ChartBGColor: initState.ChartBGColor || "",
      PieBorderWidth: initState.PieBorderWidth || 1,
      ShowDataProviderInfo: initState.ShowDataProviderInfo ?? false,
      ShowDataDelayInfo: initState.ShowDataDelayInfo ?? true,
      ShowSupplierInfo: initState.ShowSupplierInfo ?? true,
      ShowSupplierInfoLink: initState.ShowSupplierInfoLink ?? true,
      ShowDisclaimerInfo: initState.ShowDisclaimerInfo ?? true,
      ShowCookiePolicyInfo: initState.ShowCookiePolicyInfo ?? true,
      CustomMarketName: initState.CustomMarketName || "",
      CustomCurrencySign: initState.CustomCurrencySign || "",
      MainTextFont: initState.MainTextFont || "Arial",
      MainTextFontSize: initState.MainTextFontSize || "12px",
      MainTextColor: initState.MainTextColor || "#333",
      MainHeadingFont: initState.MainHeadingFont || "Arial",
      MainHeadingFontSize: initState.MainHeadingFontSize || "22px",
      MainHeadingColor: initState.MainHeadingColor || "#D14836",
      LinkColor: initState.LinkColor || "#D14836",
      LinkHoverColor: initState.LinkHoverColor || "#D52B1E",
    };
  }, [initState, basicSettings]);

  const handleFormDataChange = useCallback(() => {
    onDraftChange(form.getFieldsValue(true));
  }, [form, onDraftChange]);

  const IC_SETTING_TAB = [
    {
      index: 1,
      key: "general",
      label: "General",
      children: <GeneralSettings form={form} />,
    },
    {
      index: 2,
      key: "chart",
      label: "Chart",
      children: <ChartSetting form={form} />,
    },
    {
      index: 3,
      key: "instrumentGroups",
      label: "Instrument Groups",
      children: <InstrumentGroups form={form} onChange={handleFormDataChange} />,
    },
    {
      index: 4,
      key: "format",
      label: "Format",
      children: <FormatSettings form={form} />,
    },
    {
      index: 5,
      key: "display",
      label: "Display",
      children: <DisplaySettings form={form} />,
    },
    {
      index: 6,
      key: "customPhrase",
      label: "Custom Phrase",
      children: <CustomPhrases form={form} onChange={handleFormDataChange} />,
    },
    {
      index: 7,
      key: "styleUri",
      label: "Style Uri",
      children: <StyleURI />,
    },
    {
      index: 8,
      key: "download",
      label: "Download",
      children: <Download form={form} />,
    },
    {
      index: 9,
      key: "customCss",
      label: "Custom CSS",
      children: <CustomCSSEditor form={form} onChange={handleFormDataChange} />,
    },
  ];

  useEffect(() => {
    const data = form.getFieldsValue(true);
    onDraftChange(data);
  }, [form, onDraftChange]);

  const optionsFonts = Array?.from(
    new Map(companyFonts?.map((font) => [font.fontFamily, font])).values()
  )
    .map((font) => ({
      value: font.id,
      label: font.fontFamily,
    }))
    .concat(defaultFonts?.map((font) => ({ value: font, label: font })));
  return (
    <div>
      {submitErrors
        ? Object.values(submitErrors)
            .flat()
            .map((item, index) => (
              <Alert
                closable
                className="mt-3"
                key={index}
                message={item}
                type="error"
              />
            ))
        : null}
      <SettingsContext.Provider
        value={{
          basicSettings,
          availableTools,
          initState,
          currencies,
          optionsFonts,
          toolName,
        }}
      >
        <Form
          layout={"vertical"}
          form={form}
          initialValues={{
            ...initState,
            ...initCurrencyState,
            ...initInstrumentConfigState,
            ...initPeriodState,
            ...initGeneralState,
          }}
          onValuesChange={handleFormDataChange}
          style={{ width: "100%" }}
        >
          <Tabs
            tabPosition={"left"}
            size={"default"}
            defaultActiveKey="1"
            items={IC_SETTING_TAB}
          />
        </Form>
      </SettingsContext.Provider>
    </div>
  );
}
