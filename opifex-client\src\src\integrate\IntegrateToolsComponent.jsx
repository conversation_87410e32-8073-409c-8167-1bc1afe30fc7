import { lazy, memo, Suspense, useCallback, useImperative<PERSON><PERSON><PERSON>, useMemo, useRef, useState, Component } from "react";
import { App, Badge, List, Space, Tabs, Typography } from "antd";

import useAppSelector from "../hooks/useAppSelector";
import { getSelectedTools } from "../store/selector/formsSelector";
import GeneralToolSettings, { GENERAL_TOOL_NAME } from "../components/v2/GeneralToolSettings";
import useAllRuleExpressions from "../hooks/useAllRuleExpressions";
import { loadToolScript } from "./toolScript";
import {setUpTool} from "../store/slice/forms";
import {useDispatch} from "react-redux";
import {companyFontsSelector} from "../store/selector/settingSelector";
import { useCurrencies } from "../hooks/useSettings";
import { getApiClient } from "../services/api";

const { Paragraph } = Typography;

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render shows the fallback UI.
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // You can log the error to an error reporting service here
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      // Render any fallback UI you want
      return <h1>Something went wrong.</h1>;
    }

    return this.props.children;
  }
}

function createMemorizeComponent(Component) {
  const ToolComponent = memo(Component);

  const ToolWrapperComponent = ({ draftState, initState, onDraftChange, name, ...props }) => {
    const onDraftChangeRef = useRef();
    onDraftChangeRef.current = onDraftChange;
    return (
      <ToolComponent
        initState={useMemo(() => draftState ?? initState, [initState])}
        onDraftChange={useCallback(data => onDraftChangeRef.current?.(name, data), [])}
        {...props}
      />
    );
  };

  return ToolWrapperComponent;
}

export const setupToolComponent = (name, remoteRegisterFormsRef, ToolComponent, onToolLoaded, onError) => {
  return props => {
    const handleRef = useCallback((node) => {
      onToolLoaded?.()
      remoteRegisterFormsRef.current[name] = node;
    }, [])
    return (
      <ErrorBoundary onError={onError}>
        <Suspense fallback={"loading..."}>
          <ToolComponent handleRef={handleRef} {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  };
};

const setupToolClientApi = async toolName => {
  const script = await loadToolScript(toolName);
  if (typeof script.setApiClient === "function") {
    const apiClient = getApiClient();
    script.setApiClient(apiClient);
  }
};

const IntegrateToolsComponent = ({ remoteRegisterFormsRef, onDraftChange, handleRef }) => {
  const { modal } = App.useApp()

  const toolSettings = useAppSelector(state => state.settings.availableTools.data)
  const [{ currencies }] = useCurrencies();
  const basicSettings = useAppSelector(state => state.forms.basicSettings.initState);
  const availableTools = useAppSelector(state => state.forms.availableTools.initState);
  const companyFonts = useAppSelector(companyFontsSelector)
  // const generalSettings = useAppSelector(state => state.forms.generalSetting.initState);
  const formToolState = useAppSelector(state => state.forms.tools);
  const selectedTools = useAppSelector(getSelectedTools);
  const rules = useAllRuleExpressions();
  const dispatch = useDispatch()

  const [activeTab, setActiveTab] = useState();
  const [invalidTabs, setInvalidTabs] = useState({});

  const toolsComponent = useMemo(
    () =>
      [{ Component: GeneralToolSettings, name: GENERAL_TOOL_NAME, label: "General Tools" }]
        .concat(
          selectedTools.map(tool => {
            setupToolClientApi(tool);
            return {
              name: tool,
              label: toolSettings[tool].shortName,
              Component: lazy(() => loadToolScript(tool)),
            };
          })
        )
        .map(item => ({
          ...item,
          Component: setupToolComponent(
            item.name,
            remoteRegisterFormsRef,
            createMemorizeComponent(item.Component),
            () => {
              if (item.name === GENERAL_TOOL_NAME) return;
              const defaultSettings = toolSettings[item.name].defaultSettings;
              dispatch(setUpTool({ toolName: item.name, defaultState: defaultSettings }));
            },
            (error, errorInfo) => {
              console.error(error, errorInfo);
              modal.error({
                title: "Tool Failed to Load",
                content: <div>
                <Typography.Text>The <strong>{item.label}</strong> could not be loaded.</Typography.Text>
                <Typography.Title level={5} style={{ marginTop: 16 }}>Suggestions:</Typography.Title>
                
                <List
                  size="small"
                  bordered={false}
                  dataSource={[
                    "Try reloading the page.",
                    "If the issue persists, contact support.",
                  ]}
                  renderItem={(item) => <List.Item>{item}</List.Item>}
                />
              </div>,
                okText: "Reload Page",
                onOk: () => location.reload(),
                closable: false,
                open: true,
                keyboard: false,
                maskClosable: false
              })
            },
          ),
        })),
    [selectedTools.length]
  );

  const onChange = key => {
    setActiveTab(key);
  };

  const handleSetInvalidTab = (tool, isValid) => {
    setInvalidTabs(prev => ({ ...prev, [tool]: isValid }));
  };

  useImperativeHandle(
    handleRef,
    () => {
      return { onChangeTab: onChange, onSetInvalidTab: handleSetInvalidTab };
    },
    []
  );

  return (
    <Tabs
      activeKey={activeTab}
      onChange={onChange}
      items={toolsComponent.map(({ Component, name, label }) => {
        const defaultSettings = toolSettings[name] ? toolSettings[name].defaultSettings : undefined
        return {
          key: name,
          label: (
            <Space>
              <Paragraph style={{ marginBottom: 0 }} level={5}>
                {label}
              </Paragraph>
              {invalidTabs[name] && <Badge status="error" />}
            </Space>
          ),
          forceRender: true,
          children: (
            <Component
              basicSettings={basicSettings}
              // generalSettings={generalSettings}
              availableTools={availableTools}
              currencies={currencies}
              draftState={formToolState[name]?.draft}
              initState={formToolState[name]?.initState ?? defaultSettings}
              rule={rules[name]}
              submitErrors={formToolState[name]?.submitErrors}
              name={name}
              companyFonts={companyFonts}
              onDraftChange={onDraftChange}
            />
          ),
        };
      })}
    />
  );
};

export default IntegrateToolsComponent;
