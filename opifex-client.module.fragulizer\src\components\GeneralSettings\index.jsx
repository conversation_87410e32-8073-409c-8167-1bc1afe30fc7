import { Form, Switch, Select } from "antd";
import { useInputStyle } from "../../hooks/useInputStyle";

const PERIOD_OPTIONS = [
  { value: "5D", label: "5D" },
  { value: "1M", label: "1M" },
  { value: "3M", label: "3M" },
  { value: "6M", label: "6M" },
  { value: "1Y", label: "1Y" },
  { value: "5Y", label: "5Y" },
  { value: "10Y", label: "10Y" },
  { value: "Custom range", label: "Custom range" },
  { value: "LIVE", label: "LIVE" }
];

const GeneralSettings = ({ form }) => {
  const { inputStyle } = useInputStyle();

  return (
    <div>
            <Form.Item
        name={["EnabledPeriods"]}
        label="Enabled periods for selection"
        rules={[{ required: true, message: "Please select at least one period" }]}
      >
        <Select
          mode="multiple"
          placeholder="Select enabled periods"
          style={inputStyle}
          options={PERIOD_OPTIONS}
          allowClear
        />
      </Form.Item>

      <Form.Item
        name={["DefaultPeriod"]}
        label="Change default period for Fragmentation and Market Share sections"
        rules={[
          { required: true, message: "Please select a default period" },
          ({ getFieldValue }) => ({
            validator(_, value) {
              const enabledPeriods = getFieldValue(["EnabledPeriods"]) || [];
              if (!value || enabledPeriods.includes(value)) {
                return Promise.resolve();
              }
              return Promise.reject(new Error("Default period must be one of the enabled periods"));
            },
          }),
        ]}
        dependencies={[["EnabledPeriods"]]}
      >
        <Select
          placeholder="Select default period"
          style={inputStyle}
          options={PERIOD_OPTIONS.filter(option => {
            const enabledPeriods = form?.getFieldValue(["EnabledPeriods"]) || [];
            return enabledPeriods.length === 0 || enabledPeriods.includes(option.value);
          })}
        />
      </Form.Item>
      <Form.Item 
        name={["EnableHeading"]} 
        label="Enable/disable the heading on the User Interface"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item 
        name={["EnableInstrumentsTable"]} 
        label="Enable/disable 'Table of instruments' section"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item 
        name={["ShowCurrencyColumn"]} 
        label="Enable show Currency column"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item 
        name={["EnableActivityTrend"]} 
        label="Enable/disable 'Activity trend' section"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>
      <Form.Item 
        name={["UseLatinNumbers"]} 
        label="Use latin number for arabic language"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item 
        name={["GoogleAnalyticsEnabled"]} 
        label="Google Analytics enabled"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>
    </div>
  );
};

export default GeneralSettings;
