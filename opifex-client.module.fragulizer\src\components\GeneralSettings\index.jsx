import { Form, Switch, Select } from "antd";
import { useInputStyle } from "../../hooks/useInputStyle";

const PERIOD_OPTIONS = [
  { value: "LIVE", label: "LIVE" },
  { value: "1M", label: "1M" },
  { value: "3M", label: "3M" },
  { value: "6M", label: "6M" },
  { value: "1Y", label: "1Y" }
];

const GeneralSettings = ({ form }) => {
  const { inputStyle } = useInputStyle();

  return (
    <div>
      <Form.Item 
        name={["EnableHeading"]} 
        label="Enable/disable the heading on the User Interface"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item 
        name={["EnableInstrumentsTable"]} 
        label="Enable/disable 'Table of instruments' section"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item 
        name={["ShowCurrencyColumn"]} 
        label="Enable show Currency column"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item 
        name={["EnableActivityTrend"]} 
        label="Enable/disable 'Activity trend' section"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item 
        name={["DefaultPeriod"]} 
        label="Change default period for Fragmentation and Market Share sections"
        rules={[{ required: true, message: "Please select a default period" }]}
      >
        <Select
          placeholder="Select default period"
          style={inputStyle}
          options={PERIOD_OPTIONS}
        />
      </Form.Item>
      <Form.Item 
        name={["UseLatinNumbers"]} 
        label="Use latin number for arabic language"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item 
        name={["GoogleAnalyticsEnabled"]} 
        label="Google Analytics enabled"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>
    </div>
  );
};

export default GeneralSettings;
