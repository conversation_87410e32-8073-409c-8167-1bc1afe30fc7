import { Form, Input, Card, Row, Col } from "antd";
import { useInputStyle } from "../../hooks/useInputStyle";

export const ChartSetting = ({ form }) => {
  const { inputStyle } = useInputStyle();

  return (
    <div>
      <Card title="Chart Appearance" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name={["NumberOfYearOnColumnChart"]}
              label="Number of years on column chart"
            >
              <Input
                type="number"
                min={1}
                max={10}
                placeholder="3"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name={["ChartColors"]}
          label="Chart Colors (semicolon separated)"
        >
          <Input.TextArea
            placeholder="#2F7ED8;#0D233A;#8BBC21;#910000;#1AADCE;#4572A7;#AA4643;#89A54E;#80699B;#3D96AE;#DB843D;#92A8CD;#A47D7C;#B5CA92"
            rows={3}
            style={inputStyle}
          />
        </Form.Item>
      </Card>
    </div>
  );
};
