import { Form } from "antd";
import InstrumentIdSelect from "./InstrumentIdSelect";

export const ChartSetting = ({ form }) => {

  return (
    <div>
      <Form.Item name={["instrumentIds"]} label={"Instrument Ids"} rules={[{ required: true }]}>
        <InstrumentIdSelect form={form} />
      </Form.Item>
      <Form.Item name={["instrumentConfigs"]} label={"Enable previous"} rules={[{ required: true }]}>
        <InstrumentConfigs form={form} />
      </Form.Item>
    </div>
  );
};
