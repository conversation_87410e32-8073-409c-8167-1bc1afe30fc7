import { Form } from "antd";
import InstrumentIdSelect from "./InstrumentIdSelect";
import EnabledPeriodSelect from "./EnabledPeriodSelect";
import DefaultPeriodSelect from "./DefaultPeriodSelect";

export const ChartSetting = ({ form }) => {

  return (
    <div>
      <Form.Item name={["instrumentIds"]} label={"Instrument Ids"} rules={[{ required: true }]}>
        <InstrumentIdSelect form={form} />
      </Form.Item>
      <Form.Item name={["enabledPeriods"]} label={"Enabled Period"} rules={[{ required: true }]}>
        <EnabledPeriodSelect form={form} />
      </Form.Item>
      <Form.Item
        name={["defaultPeriod"]}
        label={"Default Period"}
        rules={[
          { required: true, message: "Please select a default period" },
          ({ getFieldValue }) => ({
            validator(_, value) {
              const enabledPeriods = getFieldValue(["enabledPeriods"]) || [];
              if (!value || enabledPeriods.includes(value)) {
                return Promise.resolve();
              }
              return Promise.reject(new Error("Default period must be one of the enabled periods"));
            },
          }),
        ]}
        dependencies={[["enabledPeriods"]]}
      >
        <DefaultPeriodSelect form={form} />
      </Form.Item>
    </div>
  );
};
