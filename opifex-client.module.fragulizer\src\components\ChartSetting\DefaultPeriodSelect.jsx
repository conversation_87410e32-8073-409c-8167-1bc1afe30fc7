import { useInputStyle } from "../../hooks/useInputStyle";
import { Select } from "antd";
import { useMemo } from "react";

const PERIOD_OPTIONS = [
  { value: "1D", label: "1D" },
  { value: "5D", label: "5D" },
  { value: "1M", label: "1M" },
  { value: "3M", label: "3M" },
  { value: "6M", label: "6M" },
  { value: "1Y", label: "1Y" },
  { value: "5Y", label: "5Y" },
  { value: "10Y", label: "10Y" },
  { value: "Custom range", label: "Custom range" },
  { value: "All", label: "All" }
];

const DefaultPeriodSelect = ({ value, onChange, form }) => {
  const { inputStyle } = useInputStyle();

  // Lấy danh sách enabled periods từ form để filter options
  const enabledPeriods = form?.getFieldValue(["enabledPeriods"]) || [];

  const filteredOptions = useMemo(() => {
    if (!enabledPeriods.length) return PERIOD_OPTIONS;
    return PERIOD_OPTIONS.filter(option => enabledPeriods.includes(option.value));
  }, [enabledPeriods]);

  const handleChange = (_value) => {
    onChange?.(_value);
  };

  return (
    <Select
      showSearch
      value={value}
      placeholder="Select default period"
      optionFilterProp="children"
      onChange={handleChange}
      style={inputStyle}
      options={filteredOptions}
      allowClear
    />
  );
};

export default DefaultPeriodSelect;
