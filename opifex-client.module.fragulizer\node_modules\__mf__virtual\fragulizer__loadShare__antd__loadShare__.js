
    
    ;() => import("__mf__virtual/fragulizer__prebuild__antd__prebuild__.js").catch(() => {});
    // dev uses dynamic import to separate chunks
    ;() => import("antd").catch(() => {});
    const {loadShare} = require("@module-federation/runtime")
    const {initPromise} = require("__mf__virtual/fragulizer__mf_v__runtimeInit__mf_v__.js")
    const res = initPromise.then(_ => loadShare("antd", {
    customShareInfo: {shareConfig:{
      singleton: true,
      strictVersion: false,
      requiredVersion: "^5.24.9"
    }}}))
    const exportModule = /*mf top-level-await placeholder replacement mf*/res.then(factory => factory())
    module.exports = exportModule
  