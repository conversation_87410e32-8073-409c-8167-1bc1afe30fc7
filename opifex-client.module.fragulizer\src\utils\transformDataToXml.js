import { convertDayjsFormatToDotnetFormat } from "@euroland/opifix-utils";

export function getLSTXmlSettings(basicSettings, state) {
  const { currencyCodes, InstrumentConfigs, ChartColors, InstrumentGroups, ...props } = state;

  const extractMultiLangField = (data, fieldName) => {
    const result = {};
    for (const [id, item] of Object.entries(data)) {
      if (item[fieldName] && typeof item[fieldName] === "object") {
        result[`_${id}`] = item[fieldName];
      }
    }
    return result;
  };

  const enabledIds = InstrumentConfigs
    ? Object?.entries(InstrumentConfigs)
        .filter(([_, value]) => value.EnabledDividendOption)
        .map(([key]) => key)
    : undefined;

  const extractConfigField = (configs, field) => {
    if (!configs) return undefined;

    return Object.entries(configs)
      .filter(([_, value]) => value[field])
      .reduce((acc, [key, value]) => {
        acc[`_${key}`] = value[field];
        return acc;
      }, {});
  };

  const updatedFormats =
    basicSettings?.Format &&
    Object.fromEntries(
      Object.entries(basicSettings.Format).map(([key, value]) => [
        key,
        {
          PercentDigits: value?.PercentDigits,
          DecimalSeparator: value?.DecimalSeparator,
          ThousandsSeparator: value?.ThousandsSeparator,
          DecimalDigits: value?.DecimalDigits,
          NegativeNumberFormat: value?.NegativeNumberFormat,
          LongDate: convertDayjsFormatToDotnetFormat(value?.LongDate),
          ShortDate: convertDayjsFormatToDotnetFormat(value?.ShortDate),
        },
      ])
    );
  // Process InstrumentGroups
  const processedInstrumentGroups = InstrumentGroups && InstrumentGroups.length > 0
    ? {
        InstrumentGroup: InstrumentGroups.map(group => ({
          Name: group.Name,
          InstrumentIDs: group.InstrumentIDs?.join(",") || ""
        }))
      }
    : undefined;

  const xml = {
    ...props,
    Format: updatedFormats,
    TimeZone: basicSettings.timezone ?? "",
    EnabledFieldTradeHistories: state?.EnabledFieldTradeHistories ?? "",
    RefreshTimeOut: basicSettings?.dataRefreshRate ?? 60,
    InstrumentIds: basicSettings?.instrumentIds?.join(","),
    DefaultCulture: basicSettings?.availableLanguages?.join(","),
    Currencies: currencyCodes?.join(","),
    ChartColors: typeof ChartColors === "string" ? ChartColors : ChartColors?.join(";"),
    EnabledDividendOption:
      enabledIds?.join(",") ?? basicSettings?.instrumentIds?.join(","),
    LimitInvestmentStartDate: extractConfigField(
      InstrumentConfigs,
      "LimitInvestmentStartDate"
    ),
    LimitStartingData: extractConfigField(
      InstrumentConfigs,
      "LimitStartingData"
    ),
    ...(processedInstrumentGroups && { InstrumentGroups: processedInstrumentGroups }),
  };
  if (basicSettings?.instrument) {
    xml["CustomInstrumentName"] = extractMultiLangField(
      basicSettings.instrument,
      "shareName"
    );
    xml["CustomMarketName"] = extractMultiLangField(
      basicSettings.instrument,
      "marketName"
    );
  }
  if (state?.EditCustomPhrases) {
    xml["CustomPhrases"] = structuredClone(state.EditCustomPhrases);
  }

  if (state?.StyleURI) {
    xml["StyleURI"] = state.StyleURI;
  }
  delete xml.InstrumentConfigs;
  delete xml.AllowedParentDomains;
  delete xml.Template;
  delete xml.Instruments;
  delete xml.RefreshTimeOut;
  delete xml.HideChart;
  delete xml.GoogleAnalyticsEnabled;
  delete xml.EnabledFieldTradeHistories;
  return xml;
}
