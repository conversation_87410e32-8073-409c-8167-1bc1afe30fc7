

export function getLSTXmlSettings(basicSettings, state) {
  const { currencyCodes, InstrumentConfigs, ChartColors, InstrumentGroups, ...props } = state;

  const extractMultiLangField = (data, fieldName) => {
    const result = {};
    for (const [id, item] of Object.entries(data)) {
      if (item[fieldName] && typeof item[fieldName] === "object") {
        result[`_${id}`] = item[fieldName];
      }
    }
    return result;
  };

  const enabledIds = InstrumentConfigs
    ? Object?.entries(InstrumentConfigs)
        .filter(([_, value]) => value.EnabledDividendOption)
        .map(([key]) => key)
    : undefined;

  const extractConfigField = (configs, field) => {
    if (!configs) return undefined;

    return Object.entries(configs)
      .filter(([_, value]) => value[field])
      .reduce((acc, [key, value]) => {
        acc[`_${key}`] = value[field];
        return acc;
      }, {});
  };


  // Process InstrumentGroups with multi-language support
  const processedInstrumentGroups = InstrumentGroups && InstrumentGroups.length > 0
    ? {
        InstrumentGroup: InstrumentGroups.map((group, index) => {
          const groupKey = `group${index + 1}`;

          // Create Name object with multi-language support
          const nameObject = {};

          // Extract multi-language names from EditCustomPhrases
          if (state.EditCustomPhrases && state.EditCustomPhrases[groupKey]) {
            Object.entries(state.EditCustomPhrases[groupKey]).forEach(([lang, name]) => {
              nameObject[lang] = name;
            });
          } else {
            // Fallback to default name
            nameObject['en-GB'] = group.Name || `Group ${index + 1}`;
          }

          return {
            Name: nameObject,
            InstrumentIDs: Array.isArray(group.InstrumentIDs)
              ? group.InstrumentIDs.join(";")
              : (group.InstrumentIDs || "")
          };
        })
      }
    : undefined;

  // Extract all instrumentIds from InstrumentGroups
  const allInstrumentIds = InstrumentGroups && InstrumentGroups.length > 0
    ? [...new Set(InstrumentGroups.flatMap(group =>
        Array.isArray(group.InstrumentIDs) ? group.InstrumentIDs : []
      ))]
    : basicSettings?.instrumentIds || [];

  // Convert timezone from IANA format to Windows format
  const convertTimezone = (timezone) => {
    const timezoneMap = {
      'Europe/Stockholm': 'W. Europe Standard Time',
      'Europe/London': 'GMT Standard Time',
      'America/New_York': 'Eastern Standard Time',
      'America/Chicago': 'Central Standard Time',
      'America/Denver': 'Mountain Standard Time',
      'America/Los_Angeles': 'Pacific Standard Time',
      // Add more mappings as needed
    };
    return timezoneMap[timezone] || timezone;
  };

  // Create Format section with basic structure
  const formatSection = {};
  if (basicSettings?.availableLanguages && basicSettings.availableLanguages.length > 0) {
    basicSettings.availableLanguages.forEach(lang => {
      formatSection[lang] = {
        ShortDate: "dd/MM/yyyy",
        LongDate: "MMM d, yyyy",
        ShortTime: "HH:mm",
        LongTime: "HH:mm.ss",
        ThousandsSeparator: ",",
        NegativeNumberFormat: "-n"
      };
    });
  }

  const xml = {
    ...props,
    TimeZone: convertTimezone(basicSettings.timezone ?? ""),
    Format: formatSection,
    EnabledFieldTradeHistories: state?.EnabledFieldTradeHistories ?? "",
    RefreshTimeOut: basicSettings?.dataRefreshRate ?? 60,
    InstrumentIds: allInstrumentIds.join(","),
    DefaultCulture: basicSettings?.availableLanguages?.join(","),
    Currencies: currencyCodes?.join(","),
    ChartColors: typeof ChartColors === "string" ? ChartColors : ChartColors?.join(";"),
    EnabledPeriods: Array.isArray(state?.EnabledPeriods) ? state.EnabledPeriods.join(",") : "",
    EnabledDividendOption:
      enabledIds?.join(",") ?? allInstrumentIds.join(","),
    LimitInvestmentStartDate: extractConfigField(
      InstrumentConfigs,
      "LimitInvestmentStartDate"
    ),
    LimitStartingData: extractConfigField(
      InstrumentConfigs,
      "LimitStartingData"
    ),
    ...(processedInstrumentGroups && { InstrumentGroups: processedInstrumentGroups }),
  };
  if (basicSettings?.instrument) {
    xml["CustomInstrumentName"] = extractMultiLangField(
      basicSettings.instrument,
      "shareName"
    );
    xml["CustomMarketName"] = extractMultiLangField(
      basicSettings.instrument,
      "marketName"
    );
  }
  if (state?.EditCustomPhrases) {
    xml["CustomPhrases"] = structuredClone(state.EditCustomPhrases);
  }

  if (state?.StyleURI) {
    xml["StyleURI"] = state.StyleURI;
  }
  delete xml.InstrumentConfigs;
  delete xml.AllowedParentDomains;
  delete xml.Template;
  delete xml.Instruments;
  delete xml.RefreshTimeOut;
  delete xml.HideChart;
  delete xml.GoogleAnalyticsEnabled;
  delete xml.EnabledFieldTradeHistories;
  return xml;
}
