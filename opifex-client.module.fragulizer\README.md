# Opifex Fragulizer Module

This is the Fragulizer module for the Opifex platform, providing a comprehensive settings management interface for fragmentation and market share analysis tools.

## Features

The module is organized into several main sections:

### 1. General Settings
- Enable/disable UI components (heading, instruments table, currency column, activity trend)
- Configure default periods for analysis
- Language and analytics settings

### 2. Chart Settings
- Chart appearance configuration (colors, background, border width)
- Column chart year configuration
- Pie chart border settings

### 3. Instrument Groups
- Create and manage instrument groups with multi-language support
- Assign instrument IDs to groups using table-based interface
- Dynamic group management with add/remove functionality
- **Primary source of instruments** for Market Share, Activity Trend, and Fragmentation
- All instruments are derived from group assignments

### 4. Display Settings
- Price color configuration (increase/decrease)
- Footer information toggles
- Custom market naming

### 6. Typography Settings
- Font configuration for different text elements
- Color settings for text, headings, and links
- Table styling options

### 6. Custom Phrases
- Multi-language phrase management
- Dynamic phrase addition and editing

### 7. Style URI & Custom CSS
- External stylesheet configuration
- Custom CSS editor with Monaco Editor

## Development

### Running the Application

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

### Testing the Components

To test the new components in isolation, you can access the demo mode by adding `?demo=true` to the URL:

```
http://localhost:4009/?demo=true
```

This will load a standalone demo with all the new components and mock data for testing.

### XML Output

The application generates XML configuration based on the form inputs. The structure follows the fragulizer XML schema with sections for:

- General settings (EnableHeading, EnableInstrumentsTable, etc.)
- Period configuration (DefaultPeriod)
- Instrument groups (InstrumentGroups/InstrumentGroup)
- Format settings (Format/[language])
- Typography settings (fonts, colors, sizes)
- Chart configuration (ChartColors, ChartBGColor, etc.)

### Component Structure

```
src/
├── components/
│   ├── ChartSetting/          # Chart configuration
│   ├── GeneralSettings/       # General application settings
│   ├── InstrumentGroups/      # Instrument group management
│   ├── FormatSettings/        # Date/time/number formatting
│   ├── DisplaySettings/       # Display and color settings
│   ├── TypographySettings/    # Font and text styling
│   ├── CustomPhrases/         # Multi-language phrases
│   ├── CustomCSSEditor/       # CSS editor
│   ├── StyleUri/              # External stylesheet config
│   └── TestDemo/              # Demo component for testing
├── utils/
│   ├── transformDataToXml.js  # XML generation logic
│   └── transformDataToCss.js  # CSS generation logic
└── context/
    └── SettingsContext.ts     # Application context
```

## Integration

This module is designed to be integrated as a micro-frontend in the larger Opifex platform. It exports the necessary functions for data transformation and API integration.
