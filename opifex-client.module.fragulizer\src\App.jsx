import React from "react";
import SettingTool from "./SettingTool";
import {
  createPrepareDataForSave,
  createSetApiClient,
  setAxiosInstance,
} from "@euroland/opifix-utils";
import { getGeneralSettings } from "./utils/transformDataToCss";
import { getLSTXmlSettings } from "./utils/transformDataToXml";
import { transformFileSettingToPackageSetting } from "./utils/transformFileSettingToPackageSetting";
import "./utils/addPluginToDayjs";

const App = (props) => {
  console.log({props});
  
  return (
    <React.StrictMode>
      <SettingTool {...props} />
    </React.StrictMode>
  );
};

export const setApiClient = createSetApiClient((apiClient) => {
  const { axiosInstance } = apiClient;
  setAxiosInstance(axiosInstance);
});

export const prepareDataForSave = createPrepareDataForSave(
  ({ basicSettings, generalTools, state, companyFonts }) => {
    const customCss = state?.customCss;
    const css = getGeneralSettings({
      generalTools,
      basicSettings,
      companyFonts,
    });
    const xml = getLSTXmlSettings(basicSettings, state);
    console.log({ basicSettings, generalTools, state, xml, css });
    
    return { xml, css, customCss, styleUri: state?.StyleURI };
  }
);

export const mapFileSettingToPackageSetting = async ({
  xmlSetting,
  cssSetting,
  generalTools,
  edittingVersion,
  companyCode,
  languageIds,
  defaultSettings,
}) => {
  const result = await transformFileSettingToPackageSetting({
    xmlSetting,
    cssSetting,
    generalTools,
    edittingVersion,
    companyCode,
    languageIds,
    defaultSettings,
  });
  return result;
};

export default App;
