import { Form, Input, Select, Card, Row, Col } from "antd";
import { useContext } from "react";
import { SettingsContext } from "../../context/SettingsContext";
import { useInputStyle } from "../../hooks/useInputStyle";

const DECIMAL_SEPARATOR_OPTIONS = [
  { value: ".", label: "Dot (.)" },
  { value: ",", label: "Comma (,)" },
  { value: " ", label: "Space ( )" }
];

const THOUSANDS_SEPARATOR_OPTIONS = [
  { value: ".", label: "Dot (.)" },
  { value: ",", label: "Comma (,)" },
  { value: " ", label: "Space ( )" }
];

const NEGATIVE_FORMAT_OPTIONS = [
  { value: "-n", label: "-n" },
  { value: "(n)", label: "(n)" },
  { value: "n-", label: "n-" }
];

const FormatSettings = ({ form }) => {
  const { inputStyle } = useInputStyle();
  const { basicSettings } = useContext(SettingsContext);
  const { availableLanguages = [] } = basicSettings;

  return (
    <div>
      <Card title="Timezone" size="small" style={{ marginBottom: 16 }}>
        <Form.Item 
          name={["TimeZone"]} 
          label="TimeZone ID"
          rules={[{ required: true, message: "Please enter timezone" }]}
        >
          <Input
            placeholder="e.g., W. Europe Standard Time"
            style={inputStyle}
          />
        </Form.Item>

        <Form.Item 
          name={["DefaultCulture"]} 
          label="Default culture and language"
          rules={[{ required: true, message: "Please select default culture" }]}
        >
          <Select
            placeholder="Select default culture"
            style={inputStyle}
            options={availableLanguages.map(lang => ({
              value: lang,
              label: lang
            }))}
          />
        </Form.Item>
      </Card>

      <Card title="Number Format" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item 
              name={["SharePriceNumberDecimalDigits"]} 
              label="Share price number decimal digits"
              rules={[{ required: true, message: "Please enter decimal digits" }]}
            >
              <Input
                type="number"
                min={0}
                max={10}
                placeholder="2"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item 
              name={["SharePricePercentageDecimalDigits"]} 
              label="Share price percentage decimal digits"
              rules={[{ required: true, message: "Please enter decimal digits" }]}
            >
              <Input
                type="number"
                min={0}
                max={10}
                placeholder="2"
                style={inputStyle}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {availableLanguages.map(language => (
        <Card 
          key={language}
          title={`Format Settings - ${language}`} 
          size="small" 
          style={{ marginBottom: 16 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                name={["Format", language, "ShortDate"]} 
                label="Short Date Format"
              >
                <Input
                  placeholder="dd MMMM yyyy"
                  style={inputStyle}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item 
                name={["Format", language, "LongDate"]} 
                label="Long Date Format"
              >
                <Input
                  placeholder="dd MMMM yyyy"
                  style={inputStyle}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                name={["Format", language, "ShortTime"]} 
                label="Short Time Format"
              >
                <Input
                  placeholder="hh:mm"
                  style={inputStyle}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item 
                name={["Format", language, "LongTime"]} 
                label="Long Time Format"
              >
                <Input
                  placeholder="hh:mm.ss"
                  style={inputStyle}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item 
                name={["Format", language, "DecimalSeparator"]} 
                label="Decimal Separator"
              >
                <Select
                  placeholder="Select separator"
                  style={inputStyle}
                  options={DECIMAL_SEPARATOR_OPTIONS}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item 
                name={["Format", language, "ThousandsSeparator"]} 
                label="Thousands Separator"
              >
                <Select
                  placeholder="Select separator"
                  style={inputStyle}
                  options={THOUSANDS_SEPARATOR_OPTIONS}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item 
                name={["Format", language, "NegativeNumberFormat"]} 
                label="Negative Number Format"
              >
                <Select
                  placeholder="Select format"
                  style={inputStyle}
                  options={NEGATIVE_FORMAT_OPTIONS}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>
      ))}

      <Card title="Custom Currency" size="small">
        <Form.Item 
          name={["CustomCurrencySign"]} 
          label="Custom currency sign"
        >
          <Input
            placeholder="Enter custom currency sign"
            style={inputStyle}
          />
        </Form.Item>
      </Card>
    </div>
  );
};

export default FormatSettings;
