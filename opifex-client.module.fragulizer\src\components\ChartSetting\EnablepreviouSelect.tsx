import { useContext, useRef } from "react";

import { useInputStyle } from "../../hooks/useInputStyle";
import { ReactAntdDraggableSelect } from "@euroland/react-antd-draggable-select";
import { SettingsContext } from "../../context/SettingsContext";
import React from "react";

const EnablepreviouSelect = ({ value, onChange, form }) => {
  const oldValue = useRef([]);
  const { inputStyle } = useInputStyle();
  const { basicSettings } = useContext(SettingsContext);
  const { instrumentIds } = basicSettings;
  
  const handleChange = (_value: string[]) => {
    const changedValue = _value.filter(
      (item) => !oldValue.current.includes(item)
    );
    changedValue.forEach((item) => {
      form.setFieldValue(["currencyEdit", item, "DecimalDigits"], 2);
      instrumentIds.forEach((id) => {
        form.setFieldValue(
          ["currencyEdit", item, "Text", id],
          item
        );
      });
    });
    onChange?.(_value);
    oldValue.current = [..._value];
  };

  return (
    <ReactAntdDraggableSelect
      sortingStrategy="vertical"
      showSearch
      value={value}
      onChange={handleChange}
      style={inputStyle}
      options={instrumentIds.map((option) => ({
        value: option,
        label: option,
      }))}
      allowClear
    />
  );
};

export default EnablepreviouSelect;


