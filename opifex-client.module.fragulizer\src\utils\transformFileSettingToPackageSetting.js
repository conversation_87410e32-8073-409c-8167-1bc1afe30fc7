import merge from "lodash.merge";

export const transformFileSettingToPackageSetting = async ({
  xmlSetting,
  cssSetting,
  generalTools,
  edittingVersion,
  companyCode,
  languageIds,
  defaultSettings,
}) => {
  try {
    const xml = JSON.parse(xmlSetting || "{}");

      const transformKeysToUpperCase = (data) => {
      if (!data || typeof data !== 'object' || Array.isArray(data)) {
        return {};
      }
      return Object.fromEntries(
        Object.entries(data).map(([key, value]) => [key.toUpperCase(), value])
      );
    }

    let toolSetting,
      instrument,
      Format,
      UseLatinNumber,
      timezone,
      dataRefreshRate,
      availableLanguages;

    // Extract instrumentIds from InstrumentGroups
    let InstrumentIds = [];
    if (xml?.instrumentGroups?.instrumentGroup) {
      const groups = Array.isArray(xml.instrumentGroups.instrumentGroup)
        ? xml.instrumentGroups.instrumentGroup
        : [xml.instrumentGroups.instrumentGroup];

      InstrumentIds = [...new Set(groups.flatMap(group => {
        const ids = group.instrumentIDs || group.InstrumentIDs || "";
        return typeof ids === "string" ? ids.split(";").filter(id => id.trim()) : [];
      }))];
    }

    // Fallback to direct instrumentIds if no groups
    if (InstrumentIds.length === 0) {
      let raw = xml?.instrumentIds;
      InstrumentIds = Array.isArray(raw)
        ? raw
        : typeof raw === "string"
        ? raw.split(",")
        : typeof raw === "number"
        ? [raw.toString()]
        : [];
    }

    const enabledDividendOption = xml?.enabledDividendOption?.split(",");

    instrument = InstrumentIds?.reduce((acc, id) => {
      acc[id] = {
        shareName: xml?.customInstrumentName?.[`_${id}`],
        marketName: xml?.customMarketName?.[`_${id}`],
      };
      return acc;
    }, {});
    dataRefreshRate = dataRefreshRate ?? 60;
    UseLatinNumber = xml?.useLatinNumber;
    timezone = xml?.timeZone;
    Format = {};

    availableLanguages = [
      ...new Set(["en-GB", ...Object.keys(Format || {})]),
    ].filter(
      (lang) => Array.isArray(languageIds) && languageIds.includes(lang)
    );

    toolSetting = {
      CustomPhrases: transformKeysToUpperCase(xml?.customPhrases),
      CustomCurrencySign: transformKeysToUpperCase(xml?.customCurrencySign),
      StyleURI: xml?.styleURI,
      CurrencyEnabled: xml?.currencyEnabled ?? false,
      DefaultTab: xml?.defaultTab,
      currencyCodes: Array.isArray(xml?.currencies)
        ? xml?.currencies
        : xml?.currencies?.split(","),
      ChartColors: xml?.chartColors?.split(";"),
      EnabledPeriods: Array.isArray(xml?.enabledPeriods)
        ? xml?.enabledPeriods
        : typeof xml?.enabledPeriods === "string"
        ? xml?.enabledPeriods.split(",")
        : ["5D", "1M", "3M", "6M", "1Y"],
      ShowDataProviderInfo: xml?.showdataproviderinfo ?? false,
      ShowDisclaimerInfo: xml?.showdisclaimerinfo ?? false,
      ShowSupplierInfo: xml?.showsupplierinfo ?? false,
      ShowSupplierInfoLink: xml?.showsupplierinfolink ?? false,
      ShowDataDelayInfo: xml?.showDataDelayInfo ?? false,
      TableEvenRowColor: xml?.tableevenrowcolor ?? null,
      TableHeadBackgroundColor: xml?.tableheadbackgroundcolor ?? null,
      TableHeadColor: xml?.tableheadcolor ?? null,
      TableHeadFont: xml?.tableheadfont ?? undefined,
      TableInlineBorderColor: xml?.tableinlinebordercolor ?? null,
      TableInlineBorderEnable: xml?.tableinlineborderenable ?? false,
      TableOddRowColor: xml?.tableoddrowcolor ?? null,
      TableOutlineBorderColor: xml?.tableoutlinebordercolor ?? null,
      TableOutlineBorderEnable: xml?.tableoutlineborderenable ?? false,
      SharePriceValueDecreaseColor: xml?.sharePriceValueDecreaseColor ?? null,
      SharePriceValueIncreaseColor: xml?.sharePriceValueIncreaseColor ?? null,
      ValueColor: xml?.ValueColor ?? null,
      LinkColor: xml?.linkColor ?? null,
      LinkHoverColor: xml?.linkHoverColor ?? null,
      MainHeadingColor: xml?.mainHeadingColor ?? null,
      MainHeadingFont: xml?.mainHeadingFont ?? null,
      MainHeadingFontSize: xml?.mainHeadingFontSize ?? null,
      MainTextColor: xml?.mainTextColor ?? null,
      MainTextFont: xml?.mainTextFont ?? null,
      MainTextFontSize: xml?.mainTextFontSize ?? null,
      InstrumentConfigs: InstrumentIds?.reduce((acc, id) => {
        acc[id] = {
          EnabledDividendOption: enabledDividendOption.includes(String(id)),
          LimitStartingData: xml?.limitStartingData?.[`_${id}`],
          LimitInvestmentStartDate: xml?.limitInvestmentStartDate?.[`_${id}`],
        };
        return acc;
      }, {}),
      customCss: cssSetting,
    };
    toolSetting = merge({}, defaultSettings, toolSetting);

    return JSON.stringify({
      availableTools: {
        companyCode,
        version: edittingVersion?.version,
        selectedTools: [edittingVersion.selectedTools],
      },
      basicSettings: {
        dataRefreshRate,
        availableLanguages,
        UseLatinNumber,
        mainFont: {
          fontFamily: "",
        },
        timezone,
        Format,
        instrumentIds: InstrumentIds,
        instrument,
      },
      tools: {
        generalTools,
        [edittingVersion.selectedTools]: toolSetting,
      },
    });
  } catch (error) {
    console.log("Error:", error);
    return null;
  }
};
