import { Checkbox, Flex, Form } from "antd";
import React from "react";

const Download = () => {
  return (
    <>
      <Flex justify="flex-start" align="center" style={{ columnGap: 16 }} wrap>
        <Form.Item name={["EnableExcelDownload"]} valuePropName="checked">
          <Checkbox>Enable excel download</Checkbox>
        </Form.Item>
        <Form.Item name={["EnablePrint"]} valuePropName="checked">
          <Checkbox>Enable print</Checkbox>
        </Form.Item>
        <Form.Item name={["EnableJPGDownload"]} valuePropName="checked">
          <Checkbox>Enable JPG download</Checkbox>
        </Form.Item>
        <Form.Item name={["EnablePDFDownload"]} valuePropName="checked">
          <Checkbox>Enable PDF download</Checkbox>
        </Form.Item>
      </Flex>
    </>
  );
};

export default Download;
