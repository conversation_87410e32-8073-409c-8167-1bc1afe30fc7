import { Form, But<PERSON>, Card, Input, Select, Space, Popconfirm } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import { useContext, useState, useEffect } from "react";
import { SettingsContext } from "../../context/SettingsContext";
import { useInputStyle } from "../../hooks/useInputStyle";

const InstrumentGroups = ({ form }) => {
  const { inputStyle } = useInputStyle();
  const { basicSettings } = useContext(SettingsContext);
  const { instrumentIds = [] } = basicSettings;
  const [groups, setGroups] = useState([]);

  useEffect(() => {
    const formGroups = form.getFieldValue(["InstrumentGroups"]) || [];
    setGroups(formGroups);
  }, [form]);

  const addGroup = () => {
    const newGroup = {
      id: Date.now(),
      Name: "",
      InstrumentIDs: []
    };
    const updatedGroups = [...groups, newGroup];
    setGroups(updatedGroups);
    form.setFieldValue(["InstrumentGroups"], updatedGroups);
  };

  const removeGroup = (groupId) => {
    const updatedGroups = groups.filter(group => group.id !== groupId);
    setGroups(updatedGroups);
    form.setFieldValue(["InstrumentGroups"], updatedGroups);
  };

  const updateGroup = (groupId, field, value) => {
    const updatedGroups = groups.map(group => 
      group.id === groupId ? { ...group, [field]: value } : group
    );
    setGroups(updatedGroups);
    form.setFieldValue(["InstrumentGroups"], updatedGroups);
  };

  const instrumentOptions = instrumentIds.map(id => ({
    value: id,
    label: id
  }));

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={addGroup}
        >
          Add Instrument Group
        </Button>
      </div>

      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {groups.map((group, index) => (
          <Card
            key={group.id}
            size="small"
            title={`Instrument Group ${index + 1}`}
            extra={
              <Popconfirm
                title="Are you sure you want to delete this group?"
                onConfirm={() => removeGroup(group.id)}
                okText="Yes"
                cancelText="No"
              >
                <Button 
                  type="text" 
                  danger 
                  icon={<DeleteOutlined />}
                  size="small"
                />
              </Popconfirm>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Form.Item
                label="Group Name"
                style={{ marginBottom: 8 }}
              >
                <Input
                  placeholder="Enter group name"
                  value={group.Name}
                  onChange={(e) => updateGroup(group.id, 'Name', e.target.value)}
                  style={inputStyle}
                />
              </Form.Item>

              <Form.Item
                label="Instrument IDs"
                style={{ marginBottom: 0 }}
              >
                <Select
                  mode="multiple"
                  placeholder="Select instrument IDs"
                  value={group.InstrumentIDs}
                  onChange={(value) => updateGroup(group.id, 'InstrumentIDs', value)}
                  options={instrumentOptions}
                  style={inputStyle}
                  showSearch
                  filterOption={(input, option) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
            </Space>
          </Card>
        ))}
      </Space>

      {groups.length === 0 && (
        <Card>
          <div style={{ textAlign: 'center', color: '#999', padding: '20px 0' }}>
            No instrument groups configured. Click "Add Instrument Group" to create one.
          </div>
        </Card>
      )}

      <Form.Item name={["InstrumentGroups"]} noStyle>
        <Input type="hidden" />
      </Form.Item>
    </div>
  );
};

export default InstrumentGroups;
