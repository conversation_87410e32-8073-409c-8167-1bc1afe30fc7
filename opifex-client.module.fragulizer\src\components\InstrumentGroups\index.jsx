import { Form, Button, Input, Table, Tooltip, Flex } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import { useContext, useState, useEffect } from "react";
import { SettingsContext } from "../../context/SettingsContext";
import { useInputStyle } from "../../hooks/useInputStyle";
import { ReactAntdDraggableSelect } from "@euroland/react-antd-draggable-select";

const DEFAULT_GROUP_NAMES = [
  { key: "group1", name: "Group 1" },
  { key: "group2", name: "Group 2" },
];

const InstrumentGroups = ({ form, onChange }) => {
  const { inputStyle, isMobile } = useInputStyle();
  const { basicSettings } = useContext(SettingsContext);
  const { instrumentIds = [], availableLanguages = [] } = basicSettings;
  const [instrumentGroups, setInstrumentGroups] = useState(
    DEFAULT_GROUP_NAMES.map((group) => group.key)
  );
  const [groupsData, setGroupsData] = useState([]);

  useEffect(() => {
    const formGroups = form.getFieldValue(["InstrumentGroups"]) || [];
    if (formGroups.length === 0) {
      // Initialize with default groups
      const defaultGroups = DEFAULT_GROUP_NAMES.map((group, index) => ({
        Name: group.name,
        InstrumentIDs: []
      }));
      form.setFieldValue(["InstrumentGroups"], defaultGroups);

      // Initialize group names for multi-language
      DEFAULT_GROUP_NAMES.forEach((group) => {
        form.setFieldValue(["InstrumentGroupNames", group.key], group.name);
        availableLanguages.forEach((lang) => {
          form.setFieldValue(["EditCustomPhrases", group.key, lang], group.name);
        });
      });
    }
    setInstrumentGroups(DEFAULT_GROUP_NAMES.map((group) => group.key));
  }, [form, availableLanguages]);

  useEffect(() => {
    setGroupsData(
      instrumentGroups?.map((keyWord, i) => ({
        key: i + 1,
        keyWord: keyWord,
        name: DEFAULT_GROUP_NAMES.find((item) => item.key === keyWord)?.name || keyWord,
      }))
    );
  }, [instrumentGroups]);

  const handleAddGroup = () => {
    const newGroupKey = `group${instrumentGroups.length + 1}`;
    const newGroupName = `Group ${instrumentGroups.length + 1}`;
    const newGroups = [...instrumentGroups, newGroupKey];

    setInstrumentGroups(newGroups);
    form.setFieldValue(["InstrumentGroupKeys"], newGroups);
    form.setFieldValue(["InstrumentGroupNames", newGroupKey], newGroupName);

    // Initialize multi-language names
    availableLanguages.forEach((lang) => {
      form.setFieldValue(["EditCustomPhrases", newGroupKey, lang], newGroupName);
    });

    // Add to InstrumentGroups array
    const currentGroups = form.getFieldValue(["InstrumentGroups"]) || [];
    form.setFieldValue(["InstrumentGroups"], [
      ...currentGroups,
      { Name: newGroupName, InstrumentIDs: [] }
    ]);

    onChange && onChange();
  };

  const handleDeleteGroup = (index) => {
    if (instrumentGroups.length <= 2) return; // Keep at least 2 groups

    const newGroups = instrumentGroups.filter((_, i) => i !== index);
    setInstrumentGroups(newGroups);
    form.setFieldValue(["InstrumentGroupKeys"], newGroups);

    // Update InstrumentGroups array
    const currentGroups = form.getFieldValue(["InstrumentGroups"]) || [];
    const updatedGroups = currentGroups.filter((_, i) => i !== index);
    form.setFieldValue(["InstrumentGroups"], updatedGroups);

    onChange && onChange();
  };

  const instrumentOptions = instrumentIds.map(id => ({
    value: id,
    label: id
  }));

  // Table columns definition
  const columns = [
    {
      title: 'Group',
      key: 'key',
      align: 'left',
      dataIndex: 'key',
      minWidth: 100,
      className: 'align-top',
      render: (key, record, index) => `Group ${index + 1}`,
    },
    {
      title: 'Group Name',
      key: 'keyWord',
      align: 'left',
      dataIndex: 'keyWord',
      minWidth: 300,
      className: 'align-top',
      render: (keyWord, record) =>
        DEFAULT_GROUP_NAMES.map((group) => group.key).includes(record.keyWord) ? (
          keyWord
        ) : (
          <Form.Item
            name={['InstrumentGroupNames', `${record.keyWord}`]}
            rules={[
              {
                required: true,
                message: 'Please enter group name!',
              },
            ]}
          >
            <Input />
          </Form.Item>
        ),
    },
    ...(availableLanguages
      ? availableLanguages.map((lang) => ({
          title: lang,
          align: 'left',
          minWidth: 300,
          dataIndex: '',
          key: '',
          render: (_, record) => (
            <Form.Item
              name={['EditCustomPhrases', `${record.keyWord}`, `${lang}`]}
              rules={[
                {
                  required: true,
                  message: `Please enter group name in ${lang}!`,
                },
              ]}
            >
              <Input />
            </Form.Item>
          ),
        }))
      : []),
    {
      title: 'Action',
      align: 'center',
      width: 100,
      fixed: !isMobile ? 'right' : false,
      dataIndex: '',
      key: '',
      render: (_, record, index) => (
        <Tooltip title={`Delete group ${record.key}`}>
          <Button
            type="primary"
            danger
            onClick={() => handleDeleteGroup(index)}
            icon={<DeleteOutlined />}
            disabled={instrumentGroups.length <= 2}
          />
        </Tooltip>
      ),
    },
  ];

  return (
    <div>
      <Flex justify="flex-end" align="center" style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddGroup}
        >
          Add Group
        </Button>
      </Flex>

      <Table
        size={isMobile ? 'small' : 'default'}
        columns={columns}
        dataSource={groupsData}
        bordered
        pagination={false}
        style={{ width: '100%', marginBottom: 16 }}
        scroll={{ x: 'max-content', y: 500 }}
        tableLayout="auto"
      />

      {/* Instrument selection for each group */}
      {instrumentGroups?.map((keyWord, index) => {
        return (
          <Form.Item
            key={`group-instruments-${keyWord}`}
            name={['InstrumentGroups', index, 'InstrumentIDs']}
            label={`Instruments for ${DEFAULT_GROUP_NAMES.find(g => g.key === keyWord)?.name || keyWord}`}
            rules={[{ required: true, message: 'Please select instruments for this group!' }]}
          >
            <ReactAntdDraggableSelect
              sortingStrategy="vertical"
              mode="multiple"
              placeholder="Select instruments for this group"
              options={instrumentOptions}
              style={inputStyle}
              showSearch
              filterOption={(input, option) =>
                option.label.toLowerCase().includes(input.toLowerCase())
              }
            />
          </Form.Item>
        );
      })}

      {/* Hidden fields for form state management */}
      <Form.Item name={["InstrumentGroupKeys"]} noStyle>
        <Input type="hidden" />
      </Form.Item>
    </div>
  );
};

export default InstrumentGroups;
